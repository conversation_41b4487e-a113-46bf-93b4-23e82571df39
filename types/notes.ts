export interface NoteAttachment {
  id: string
  note_id: string
  url: string
  created_at: string
}

export interface Note {
  id: string
  content: string
  content_type: 'text' | 'file' | 'link'
  tags: string[]
  created_at: string
  updated_at: string
  summary_ai: string | null
  attachments: NoteAttachment[]
  isAnalyzing?: boolean
  aiAnalysisFailed?: boolean
}

export interface CreateNoteRequest {
  content: string
  content_type?: 'text' | 'file' | 'link'
  attachment_urls?: string[]
}

export interface NotesResponse {
  notes: Note[]
}

export interface CreateNoteResponse {
  note: Note
}

export interface UpdateNoteRequest {
  content: string
}

export interface UpdateNoteResponse {
  note: Note
}

export interface TagsResponse {
  tags: string[]
}
