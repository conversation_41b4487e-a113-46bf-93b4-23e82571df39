-- Add support for multimodal AI analysis with multiple images
-- This migration adds partial completion status and error tracking for individual attachments

-- Step 1: Update ai_analysis_status constraint to include 'partially_completed'
-- Remove the old constraint and add a new one with the additional value
ALTER TABLE notes DROP CONSTRAINT IF EXISTS notes_ai_analysis_status_check;
ALTER TABLE notes ADD CONSTRAINT notes_ai_analysis_status_check
CHECK (ai_analysis_status IN ('pending', 'processing', 'completed', 'failed', 'partially_completed'));

-- Step 2: Add analysis_error column to note_attachments table
-- This will store error messages when individual attachments fail to process
ALTER TABLE note_attachments ADD COLUMN analysis_error TEXT NULL;

-- Step 3: Add index for querying attachments with errors
-- This will help with performance when finding failed attachments
CREATE INDEX idx_note_attachments_analysis_error ON note_attachments(analysis_error)
WHERE analysis_error IS NOT NULL;

-- Step 4: Add comments to document the new functionality
COMMENT ON COLUMN note_attachments.analysis_error IS 'Stores raw error message if this attachment failed during AI analysis';

-- Step 5: Update table comment to reflect new capabilities
COMMENT ON TABLE note_attachments IS 'Stores multiple image attachments for notes (max 3 per note). Supports individual error tracking for AI analysis.';

-- Step 6: Add constraint to ensure analysis_error is only set when there's actually an error
-- This prevents empty strings from being stored
ALTER TABLE note_attachments ADD CONSTRAINT check_analysis_error_not_empty
CHECK (analysis_error IS NULL OR length(trim(analysis_error)) > 0);

-- Step 7: Update comment for ai_analysis_status to include new value
COMMENT ON COLUMN notes.ai_analysis_status IS 'Status of AI analysis: pending, processing, completed, failed, partially_completed';
