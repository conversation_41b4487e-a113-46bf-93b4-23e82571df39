-- Migrate existing attachment_url data from notes table to note_attachments table
-- This migration preserves all existing image attachments

-- Step 1: Migrate existing attachment URLs to note_attachments table
INSERT INTO note_attachments (note_id, url, created_at)
SELECT 
    id as note_id,
    attachment_url as url,
    created_at
FROM notes 
WHERE attachment_url IS NOT NULL 
AND attachment_url != '';

-- Step 2: Verify migration was successful
-- This will show count of migrated attachments
DO $$
DECLARE
    original_count INTEGER;
    migrated_count INTEGER;
BEGIN
    -- Count original attachments
    SELECT COUNT(*) INTO original_count 
    FROM notes 
    WHERE attachment_url IS NOT NULL AND attachment_url != '';
    
    -- Count migrated attachments
    SELECT COUNT(*) INTO migrated_count 
    FROM note_attachments;
    
    -- Log the results
    RAISE NOTICE 'Migration completed: % original attachments, % migrated attachments', 
        original_count, migrated_count;
    
    -- Verify counts match
    IF original_count != migrated_count THEN
        RAISE EXCEPTION 'Migration verification failed: counts do not match (% vs %)', 
            original_count, migrated_count;
    END IF;
END $$;

-- Step 3: Remove the attachment_url column from notes table
-- This is done after successful migration to prevent data loss
ALTER TABLE notes DROP COLUMN attachment_url;

-- Step 4: Drop the old index that was created for attachment_url
DROP INDEX IF EXISTS idx_notes_with_attachments;

-- Add comment about the migration
COMMENT ON TABLE note_attachments IS 'Stores multiple image attachments for notes (max 3 per note). Migrated from notes.attachment_url field.';
