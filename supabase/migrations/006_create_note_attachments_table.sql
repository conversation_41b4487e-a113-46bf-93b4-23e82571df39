-- Create note_attachments table for multiple image attachments per note
-- This replaces the single attachment_url field in notes table

-- Create the note_attachments table
CREATE TABLE note_attachments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    note_id UUID REFERENCES notes(id) ON DELETE CASCADE NOT NULL,
    url TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()) NOT NULL
);

-- Create indexes for better performance
CREATE INDEX idx_note_attachments_note_id ON note_attachments(note_id);
CREATE INDEX idx_note_attachments_created_at ON note_attachments(created_at);

-- Enable Row Level Security (RLS)
ALTER TABLE note_attachments ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for note_attachments
-- Users can view attachments of their own notes
CREATE POLICY "Users can view attachments of their notes" ON note_attachments
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM notes 
            WHERE notes.id = note_attachments.note_id 
            AND notes.user_id = auth.uid()
        )
    );

-- Users can create attachments for their own notes
CREATE POLICY "Users can create attachments for their notes" ON note_attachments
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM notes 
            WHERE notes.id = note_attachments.note_id 
            AND notes.user_id = auth.uid()
        )
    );

-- Users can delete attachments of their own notes
CREATE POLICY "Users can delete attachments of their notes" ON note_attachments
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM notes 
            WHERE notes.id = note_attachments.note_id 
            AND notes.user_id = auth.uid()
        )
    );

-- Add constraint to limit maximum 3 attachments per note
-- This is implemented as a function and trigger for better performance
CREATE OR REPLACE FUNCTION check_max_attachments_per_note()
RETURNS TRIGGER AS $$
BEGIN
    IF (SELECT COUNT(*) FROM note_attachments WHERE note_id = NEW.note_id) >= 3 THEN
        RAISE EXCEPTION 'Maximum 3 attachments allowed per note';
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_check_max_attachments
    BEFORE INSERT ON note_attachments
    FOR EACH ROW
    EXECUTE FUNCTION check_max_attachments_per_note();

-- Add comment to document the table purpose
COMMENT ON TABLE note_attachments IS 'Stores multiple image attachments for notes (max 3 per note)';
COMMENT ON COLUMN note_attachments.note_id IS 'Reference to the note this attachment belongs to';
COMMENT ON COLUMN note_attachments.url IS 'Public URL of the attachment file in Supabase Storage';
