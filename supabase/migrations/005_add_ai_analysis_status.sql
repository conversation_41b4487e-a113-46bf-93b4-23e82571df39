-- Add ai_analysis_status column to track AI analysis state
ALTER TABLE notes ADD COLUMN ai_analysis_status TEXT DEFAULT 'pending' CHECK (ai_analysis_status IN ('pending', 'processing', 'completed', 'failed'));

-- Create index for better performance when querying by status
CREATE INDEX idx_notes_ai_analysis_status ON notes(ai_analysis_status);

-- Update existing notes based on their current state
UPDATE notes SET ai_analysis_status = CASE
  WHEN summary_ai IS NOT NULL OR EXISTS (
    SELECT 1 FROM note_tags WHERE note_tags.note_id = notes.id
  ) THEN 'completed'
  ELSE 'pending'
END;

-- Add comment to document the column purpose
COMMENT ON COLUMN notes.ai_analysis_status IS 'Status of AI analysis: pending, processing, completed, failed';
