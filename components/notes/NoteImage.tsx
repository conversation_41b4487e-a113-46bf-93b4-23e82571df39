interface NoteImageProps {
  attachmentUrl: string
  alt?: string
  className?: string
}

export function NoteImage({ attachmentUrl, alt = "Прикрепленное изображение", className }: NoteImageProps) {
  return (
    <div className="rounded-lg overflow-hidden border">
      <img
        src={attachmentUrl}
        alt={alt}
        className={className || "w-full max-h-96 object-contain bg-muted"}
        loading="lazy"
        onError={(e) => {
          const target = e.target as HTMLImageElement
          target.style.display = 'none'
          const parent = target.parentElement
          if (parent) {
            parent.innerHTML = `
              <div class="p-4 bg-muted text-center text-muted-foreground">
                <div class="text-sm">Не удалось загрузить изображение</div>
              </div>
            `
          }
        }}
      />
    </div>
  )
}
