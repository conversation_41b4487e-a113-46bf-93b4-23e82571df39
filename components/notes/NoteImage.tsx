import { AlertCircle } from "lucide-react"

// Constants
const DEFAULT_ALT_TEXT = "Прикрепленное изображение"
const DEFAULT_IMAGE_CLASSES = "w-full max-h-96 object-contain bg-muted"
const ERROR_TOOLTIP = "Ошибка анализа изображения"
const LOAD_ERROR_MESSAGE = "Не удалось загрузить изображение"

interface NoteImageProps {
  attachmentUrl: string
  alt?: string
  className?: string
  hasAnalysisError?: boolean
}

/**
 * Handle image load error by replacing with error message
 */
function handleImageLoadError(event: React.SyntheticEvent<HTMLImageElement>) {
  const target = event.target as HTMLImageElement
  target.style.display = 'none'
  const parent = target.parentElement
  if (parent) {
    parent.innerHTML = `
      <div class="p-4 bg-muted text-center text-muted-foreground">
        <div class="text-sm">${LOAD_ERROR_MESSAGE}</div>
      </div>
    `
  }
}

export function NoteImage({ attachmentUrl, alt = DEFAULT_ALT_TEXT, className, hasAnalysisError }: NoteImageProps) {
  return (
    <div className="rounded-lg overflow-hidden border relative">
      {hasAnalysisError && (
        <div className="absolute top-2 right-2 z-10">
          <div className="bg-destructive text-destructive-foreground rounded-full p-1" title={ERROR_TOOLTIP}>
            <AlertCircle className="h-4 w-4" />
          </div>
        </div>
      )}
      <img
        src={attachmentUrl}
        alt={alt}
        className={className || DEFAULT_IMAGE_CLASSES}
        loading="lazy"
        onError={handleImageLoadError}
      />
    </div>
  )
}
