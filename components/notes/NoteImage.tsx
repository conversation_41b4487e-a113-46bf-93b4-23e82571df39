import { AlertCircle } from "lucide-react"

interface NoteImageProps {
  attachmentUrl: string
  alt?: string
  className?: string
  hasAnalysisError?: boolean
}

export function NoteImage({ attachmentUrl, alt = "Прикрепленное изображение", className, hasAnalysisError }: NoteImageProps) {
  return (
    <div className="rounded-lg overflow-hidden border relative">
      {hasAnalysisError && (
        <div className="absolute top-2 right-2 z-10">
          <div className="bg-destructive text-destructive-foreground rounded-full p-1" title="Ошибка анализа изображения">
            <AlertCircle className="h-4 w-4" />
          </div>
        </div>
      )}
      <img
        src={attachmentUrl}
        alt={alt}
        className={className || "w-full max-h-96 object-contain bg-muted"}
        loading="lazy"
        onError={(e) => {
          const target = e.target as HTMLImageElement
          target.style.display = 'none'
          const parent = target.parentElement
          if (parent) {
            parent.innerHTML = `
              <div class="p-4 bg-muted text-center text-muted-foreground">
                <div class="text-sm">Не удалось загрузить изображение</div>
              </div>
            `
          }
        }}
      />
    </div>
  )
}
