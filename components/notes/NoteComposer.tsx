import { useState, useRef } from "react"
import { Send, Edit3, <PERSON>c<PERSON>, ImageIcon, X } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent } from "@/components/ui/card"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { InlineError } from "@/components/ui/inline-error"
import { useMultipleFileUpload } from "@/hooks/useFileUpload"
import { ATTACHMENT_LIMITS, ATTACHMENT_UI_TEXT, UPLOAD_CONFIG } from "@/lib/config/attachments"
import ReactMarkdown from "react-markdown"
import remarkGfm from "remark-gfm"

// Types
interface NoteComposerProps {
  onSubmit: (content: string, attachmentUrls?: string[]) => Promise<void>
  userEmail?: string
  isSubmitting?: boolean
}

export function NoteComposer({ onSubmit, userEmail, isSubmitting = false }: NoteComposerProps) {
  const [content, setContent] = useState("")
  const [isMarkdownMode, setIsMarkdownMode] = useState(false)
  const [submitting, setSubmitting] = useState(false)
  const [fileError, setFileError] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const {
    selectedFiles,
    uploadingFiles,
    addFile,
    removeFile,
    uploadAll,
    canAddMore
  } = useMultipleFileUpload(ATTACHMENT_LIMITS.MAX_PER_NOTE)

  // Helper function to generate error message for too many files
  const getTooManyFilesError = (selectedCount: number, attemptedCount: number): string => {
    const remainingSlots = ATTACHMENT_LIMITS.MAX_PER_NOTE - selectedCount

    if (remainingSlots === 0) {
      return ATTACHMENT_UI_TEXT.TOO_MANY_FILES_NO_SLOTS
    }

    return ATTACHMENT_UI_TEXT.TOO_MANY_FILES_WITH_SLOTS(remainingSlots, attemptedCount)
  }

  // Helper function to validate file selection
  const validateFileSelection = (files: FileList): string | null => {
    const remainingSlots = ATTACHMENT_LIMITS.MAX_PER_NOTE - selectedFiles.length

    if (files.length > remainingSlots) {
      return getTooManyFilesError(selectedFiles.length, files.length)
    }

    return null
  }

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (!files || files.length === 0) return

    setFileError(null)

    // Validate file selection
    const validationError = validateFileSelection(files)
    if (validationError) {
      setFileError(validationError)
      return
    }

    // Add each selected file
    for (let i = 0; i < files.length; i++) {
      const file = files[i]
      const result = addFile(file)

      if (!result.success) {
        setFileError(result.error || 'Ошибка при выборе файла')
        break
      }
    }
  }

  const handleRemoveFile = (fileId: string) => {
    removeFile(fileId)
    setFileError(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }



  // Helper function to upload files and handle errors
  const uploadFilesIfNeeded = async (): Promise<string[]> => {
    if (selectedFiles.length === 0) {
      return []
    }

    try {
      const attachmentUrls = await uploadAll()
      if (selectedFiles.length > 0 && attachmentUrls.length === 0) {
        throw new Error('Загрузка файлов не удалась')
      }
      return attachmentUrls
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Ошибка загрузки файлов'
      setFileError(errorMessage)
      throw error
    }
  }

  // Helper function to clear form after successful submission
  const clearForm = () => {
    setContent("")
    setIsMarkdownMode(false)
    selectedFiles.forEach(file => handleRemoveFile(file.id))
  }

  // Helper function to check if submission is allowed
  const canSubmit = (): boolean => {
    const hasContent = Boolean(content.trim()) || selectedFiles.length > 0
    const notBusy = !submitting && !isSubmitting && !uploadingFiles
    return hasContent && notBusy
  }

  const handleSubmit = async () => {
    if (!canSubmit()) return

    setSubmitting(true)
    setFileError(null)

    try {
      const attachmentUrls = await uploadFilesIfNeeded()
      await onSubmit(content.trim(), attachmentUrls.length > 0 ? attachmentUrls : undefined)
      clearForm()
    } catch (error) {
      // Error already handled in uploadFilesIfNeeded
      console.error('Submit error:', error)
    } finally {
      setSubmitting(false)
    }
  }

  const isDisabled = !canSubmit()

  return (
    <Card className="py-1">
      <CardContent className="p-4">
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Avatar className="h-8 w-8">
                <AvatarFallback>{userEmail?.[0]?.toUpperCase() || 'U'}</AvatarFallback>
              </Avatar>
              <span className="text-sm font-medium">Новая заметка</span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMarkdownMode(!isMarkdownMode)}
              className={isMarkdownMode ? "bg-muted" : ""}
            >
              <Edit3 className="h-4 w-4 mr-1" />
              Markdown
            </Button>
          </div>

          <Textarea
            placeholder={isMarkdownMode ? "Введите текст с поддержкой Markdown..." : ATTACHMENT_UI_TEXT.PLACEHOLDER}
            value={content}
            onChange={(e) => setContent(e.target.value)}
            className="min-h-[100px] resize-none"
          />

          {/* Hidden file input */}
          <input
            ref={fileInputRef}
            type="file"
            accept={UPLOAD_CONFIG.ACCEPTED_TYPES}
            multiple={UPLOAD_CONFIG.MULTIPLE}
            onChange={handleFileSelect}
            className="hidden"
            disabled={!canAddMore}
          />

          {/* Files preview */}
          {selectedFiles.length > 0 && (
            <div className="space-y-2">
              <div className="text-xs text-muted-foreground flex items-center justify-between">
                <span>{ATTACHMENT_UI_TEXT.FILES_COUNTER(selectedFiles.length)}</span>
                {selectedFiles.length >= ATTACHMENT_LIMITS.MAX_PER_NOTE && (
                  <span className="text-amber-600 font-medium">{ATTACHMENT_UI_TEXT.LIMIT_REACHED}</span>
                )}
              </div>
              {selectedFiles.map((fileWithPreview) => (
                <div key={fileWithPreview.id} className="border rounded-md p-3 bg-muted/50">
                  <div className="flex items-center justify-between mb-2">
                    <div className="text-xs text-muted-foreground">
                      {fileWithPreview.file.name} ({Math.round(fileWithPreview.file.size / 1024)} KB)
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveFile(fileWithPreview.id)}
                      className="h-6 w-6 p-0"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                  <div className="relative">
                    <img
                      src={fileWithPreview.previewUrl}
                      alt="Preview"
                      className="max-w-full max-h-48 rounded object-contain"
                    />
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* File error display */}
          {fileError && (
            <InlineError message={fileError} />
          )}

          {isMarkdownMode && content && (
            <div className="border rounded-md p-3 bg-muted/50">
              <div className="text-xs text-muted-foreground mb-2">Предварительный просмотр:</div>
              <div className="prose max-w-none">
                <ReactMarkdown remarkPlugins={[remarkGfm]}>
                  {content}
                </ReactMarkdown>
              </div>
            </div>
          )}

          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Button variant="ghost" size="sm" disabled>
                <Paperclip className="h-4 w-4 mr-1" />
                Файл
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => fileInputRef.current?.click()}
                disabled={uploadingFiles || !canAddMore}
              >
                <ImageIcon className="h-4 w-4 mr-1" />
                {ATTACHMENT_UI_TEXT.BUTTON_TEXT_WITH_LIMIT(canAddMore)}
              </Button>
            </div>
            <Button onClick={handleSubmit} disabled={isDisabled}>
              <Send className="h-4 w-4 mr-1" />
              {uploadingFiles ? 'Загрузка...' : (submitting || isSubmitting) ? 'Сохранение...' : 'Отправить'}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
