# Задача 017: Загрузка изображений и мультимодальный AI анализ

## Описание задачи
Реализовать функциональность загрузки изображений в заметки с последующим мультимодальным анализом через AI. Пользователи смогут прикреплять изображения к заметкам, а AI будет анализировать как текст, так и изображение для генерации релевантных тегов и описания.

## Анализ текущего состояния

### Существующая архитектура:
- **Frontend**: Next.js с React, Tailwind CSS, shadcn/ui компоненты
- **Backend**: Next.js API Routes с Data Access Layer (DAL)
- **База данных**: Supabase PostgreSQL с таблицами:
  - `notes` (id, user_id, content_type, content, summary_ai, created_at, updated_at)
  - `tags` (id, name)
  - `note_tags` (note_id, tag_id)
- **AI**: Vercel AI SDK с OpenRouter, мультимодальная модель `google/gemini-2.5-flash-lite-preview-06-17`
- **Хранилище**: Supabase Storage (не настроено для изображений)

### Существующие компоненты:
- `NoteComposer` - создание заметок (кнопка "Изображение" отключена)
- `NotesList` - отображение заметок
- `SearchAndFilter` - поиск с AI поддержкой
- AI система: `lib/ai/analyze.ts`, `lib/ai/search.ts`
- DAL: `lib/data/notes.ts`, `lib/data/tags.ts`

## Цели
- ✅ Настроить Supabase Storage для изображений
- ✅ Добавить поле `attachment_url` в таблицу `notes`
- ✅ Реализовать загрузку изображений через signed URLs
- ✅ Обновить UI для выбора и отображения изображений
- ✅ Расширить AI анализ для мультимодального контента
- ✅ Обеспечить безопасность и производительность

## Детальный план реализации

### Этап 1: Настройка Supabase Storage и схемы БД
- [x] Создать bucket `note_attachments` в Supabase Storage
- [x] Настроить RLS политики для bucket
- [x] Создать миграцию `004_add_attachment_url_to_notes.sql`
- [x] Добавить поле `attachment_url TEXT NULL` в таблицу `notes`

### Этап 2: API для загрузки файлов
- [x] Создать API endpoint `POST /api/notes/upload-url`
- [x] Реализовать генерацию signed URLs для загрузки
- [x] Добавить валидацию типов файлов (только изображения)
- [x] Обновить API `POST /api/notes` для поддержки `attachment_url`

### Этап 3: Обновление Data Access Layer
- [x] Обновить типы в `lib/data/types.ts`
- [x] Расширить функции в `lib/data/notes.ts`
- [x] Добавить функции для работы с attachments

### Этап 4: Обновление UI компонентов
- [x] Активировать кнопку "Изображение" в `NoteComposer`
- [x] Добавить логику выбора и предпросмотра файла
- [x] Реализовать процесс загрузки с индикатором прогресса
- [x] Обновить `NotesList` для отображения изображений
- [x] Добавить возможность удаления прикрепленного изображения

### Этап 5: Мультимодальный AI анализ
- [x] Обновить `lib/ai/analyze.ts` для поддержки изображений
- [x] Расширить промпты в `lib/ai/prompts.ts`
- [x] Обновить схемы валидации для мультимодального контента
- [x] Протестировать анализ изображений с текстом

### Этап 6: Тестирование и оптимизация
- [ ] Протестировать полный цикл загрузки и анализа
- [ ] Проверить безопасность и RLS политики
- [ ] Оптимизировать размеры изображений
- [ ] Добавить обработку ошибок

## Технические детали

### Новые API endpoints:
- `POST /api/notes/upload-url` - генерация signed URL для загрузки
- Обновление `POST /api/notes` - поддержка attachment_url

### Структура файлов:
```
lib/
├── storage/
│   ├── config.ts          # Конфигурация Supabase Storage
│   ├── upload.ts          # Утилиты для загрузки файлов
│   └── validation.ts      # Валидация файлов
```

### Обновления схемы БД:
```sql
-- Добавление поля для URL изображения
ALTER TABLE notes ADD COLUMN attachment_url TEXT NULL;

-- Индекс для поиска заметок с изображениями
CREATE INDEX idx_notes_with_attachments ON notes(attachment_url) WHERE attachment_url IS NOT NULL;
```

### Конфигурация Supabase Storage:
- Bucket: `note_attachments`
- Максимальный размер файла: 10MB
- Поддерживаемые форматы: JPEG, PNG, WebP, GIF
- RLS политики для доступа только к собственным файлам

### Мультимодальный промпт:
```
Проанализируй заметку, которая содержит:
- Текст: {noteContent}
- Изображение: [прикрепленное изображение]

Сгенерируй релевантные теги и краткое описание, учитывая как текстовое содержимое, так и визуальную информацию.
```

## Риски и ограничения

### Технические риски:
- Увеличение стоимости хранения в Supabase Storage
- Возможные задержки при загрузке больших изображений
- Ограничения мультимодальной модели на размер изображений

### Минимизация рисков:
- Ограничение размера файлов (10MB)
- Сжатие изображений на клиенте
- Асинхронная обработка AI анализа
- Fallback на текстовый анализ при ошибках

## Переменные окружения
Используются существующие:
```
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
OPENROUTER_API_KEY=your_openrouter_api_key
```

## Реализованные файлы

### Новые файлы:
- `supabase/migrations/004_add_attachment_url_to_notes.sql` - миграция для добавления поля attachment_url
- `lib/storage/config.ts` - конфигурация Supabase Storage
- `lib/storage/validation.ts` - валидация файлов и утилиты
- `lib/storage/upload.ts` - функции для работы с загрузкой файлов
- `app/api/notes/upload-url/route.ts` - API endpoint для генерации signed URLs

### Обновленные файлы:
- `lib/data/types.ts` - добавлено поле attachment_url в DatabaseNote
- `types/notes.ts` - добавлено поле attachment_url в Note и CreateNoteRequest
- `lib/data/notes.ts` - обновлены функции для поддержки attachment_url
- `app/api/notes/route.ts` - поддержка attachment_url при создании заметок
- `components/notes/NoteComposer.tsx` - полная реализация загрузки изображений
- `components/notes/NotesList.tsx` - отображение прикрепленных изображений
- `app/notes/page.tsx` - передача attachment_url в handleNoteSubmit
- `lib/ai/analyze.ts` - мультимодальный анализ с поддержкой изображений
- `lib/ai/prompts.ts` - обновленные промпты для мультимодального анализа

## Дополнительные файлы документации
- `docs/tasks/017-testing-checklist.md` - чеклист для тестирования функциональности
- `docs/guides/image-upload-guide.md` - руководство пользователя по загрузке изображений

## Ключевые особенности реализации

### 1. Безопасность
- Использование signed URLs для прямой загрузки в Supabase Storage
- RLS политики для изоляции файлов пользователей
- Валидация типов и размеров файлов на клиенте и сервере

### 2. Производительность
- Прямая загрузка файлов в хранилище (минуя сервер)
- Lazy loading изображений в ленте заметок
- Асинхронный AI анализ в фоновом режиме

### 3. Пользовательский опыт
- Предпросмотр изображений перед отправкой
- Индикаторы загрузки и сохранения
- Возможность создания заметок только с изображениями
- Интуитивное удаление прикрепленных файлов

### 4. Мультимодальный AI
- Анализ как текстового, так и визуального контента
- Генерация релевантных тегов на основе изображений
- Создание описаний, учитывающих связь текста и изображения

## Статус: Реализовано ✅
Основная функциональность загрузки изображений и мультимодального AI анализа полностью реализована. Создана документация и чеклист для тестирования. Готово к использованию.
