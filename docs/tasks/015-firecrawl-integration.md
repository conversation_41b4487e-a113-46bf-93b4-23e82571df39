# Задача 015: Интеграция с Firecrawl для парсинга ссылок

## Описание задачи
Интегрировать Firecrawl API для автоматического парсинга содержимого веб-страниц при создании заметок со ссылками. Это позволит AI анализировать полное содержимое страницы вместо только URL, что значительно улучшит качество генерируемых тегов и саммари.

## Анализ текущего состояния

### Существующая архитектура:
- **Создание заметок**: API route `/api/notes` с поддержкой `content_type: 'text' | 'file' | 'link'`
- **AI анализ**: Функция `analyzeNoteInBackground()` в `lib/data/notes.ts` запускается для `content_type === 'text'`
- **AI модуль**: `lib/ai/analyze.ts` с функцией `analyzeNote()` для обработки текстового контента
- **Типы заметок**: Поддержка типа `'link'` уже существует в типах, но не используется

### Текущий процесс создания заметки:
1. Пользователь вводит текст в `NoteComposer`
2. Отправляется POST запрос на `/api/notes` с `content_type = 'text'`
3. Заметка сохраняется в БД
4. Если есть `OPENROUTER_API_KEY`, запускается `analyzeNoteInBackground()` для анализа

## Техническое решение

### 1. Установка и настройка Firecrawl
- Установить пакет `firecrawl-js`
- Добавить переменную окружения `FIRECRAWL_API_KEY`
- Создать модуль `lib/firecrawl/client.ts` для работы с API

### 2. Определение URL в заметках
- Создать утилиту `lib/utils/url.ts` для проверки наличия URL в тексте
- Поддержать различные форматы URL (http, https, www)

### 3. Модификация процесса создания заметок
- Обновить логику в `/api/notes/route.ts`:
  - Проверять наличие URL в содержимом заметки
  - Если URL найден, устанавливать `content_type = 'link'`
  - Запускать парсинг Firecrawl в фоновом режиме

### 4. Интеграция с AI анализом
- Создать новую функцию `analyzeLinkInBackground()` в `lib/data/notes.ts`
- Модифицировать `analyzeNoteInBackground()` для поддержки ссылок
- Обновить `lib/ai/analyze.ts` для работы с контентом от Firecrawl

### 5. Обработка ошибок и fallback
- Если Firecrawl недоступен или парсинг не удался, использовать оригинальный URL для анализа
- Логирование ошибок парсинга
- Таймауты для предотвращения зависания

## Детальный план реализации

### Этап 1: Настройка Firecrawl
- [ ] Установить `firecrawl-js` через pnpm
- [ ] Добавить `FIRECRAWL_API_KEY` в `.env.example`
- [ ] Создать `lib/firecrawl/client.ts` с конфигурацией клиента
- [ ] Создать `lib/firecrawl/scraper.ts` с функциями парсинга

### Этап 2: Утилиты для работы с URL
- [ ] Создать `lib/utils/url.ts` с функциями:
  - `extractUrls(text: string): string[]` - извлечение URL из текста
  - `isValidUrl(url: string): boolean` - валидация URL
  - `normalizeUrl(url: string): string` - нормализация URL

### Этап 3: Модификация API создания заметок
- [ ] Обновить `/api/notes/route.ts`:
  - Добавить проверку наличия URL в содержимом
  - Автоматически устанавливать `content_type = 'link'` при наличии URL
  - Запускать парсинг для ссылок

### Этап 4: Интеграция с AI анализом
- [ ] Создать `analyzeLinkInBackground()` в `lib/data/notes.ts`
- [ ] Обновить `analyzeNoteInBackground()` для поддержки разных типов контента
- [ ] Модифицировать `lib/ai/analyze.ts` для работы с парсенным контентом

### Этап 5: Обработка ошибок и тестирование
- [ ] Добавить обработку ошибок парсинга
- [ ] Реализовать fallback на оригинальный URL
- [ ] Добавить логирование операций Firecrawl
- [ ] Протестировать с различными типами сайтов

## Структура новых файлов

### `lib/firecrawl/client.ts`
```typescript
import FirecrawlApp from 'firecrawl-js'

export const firecrawl = new FirecrawlApp({
  apiKey: process.env.FIRECRAWL_API_KEY
})
```

### `lib/firecrawl/scraper.ts`
```typescript
export interface ScrapeResult {
  success: boolean
  content?: string
  title?: string
  error?: string
}

export async function scrapeUrl(url: string): Promise<ScrapeResult>
```

### `lib/utils/url.ts`
```typescript
export function extractUrls(text: string): string[]
export function isValidUrl(url: string): boolean
export function normalizeUrl(url: string): string
```

## Переменные окружения
```env
FIRECRAWL_API_KEY=your_firecrawl_api_key_here
```

## Ожидаемые результаты
1. Автоматическое определение ссылок в заметках
2. Парсинг содержимого веб-страниц через Firecrawl
3. Улучшенное качество AI анализа для ссылок
4. Отказоустойчивость при недоступности Firecrawl
5. Сохранение производительности создания заметок

## Риски и их минимизация
- **Зависимость от внешнего сервиса**: Реализован fallback на анализ оригинального URL
- **Производительность**: Парсинг выполняется в фоновом режиме
- **Стоимость API**: Парсинг только при наличии валидных URL
- **Таймауты**: Установлены разумные лимиты времени для запросов

## Прогресс
- [x] Файл задачи создан
- [x] Firecrawl клиент настроен
- [x] URL утилиты реализованы
- [x] API создания заметок обновлен
- [x] AI анализ интегрирован
- [x] Тестирование завершено
- [x] Рефакторинг и оптимизация кода

## Выполненная работа

### Этап 1: Настройка Firecrawl ✅
- Установлен пакет `@mendable/firecrawl-js`
- Добавлена переменная `FIRECRAWL_API_KEY` в `.env.example`
- Создан `lib/firecrawl/client.ts` с конфигурацией клиента
- Создан `lib/firecrawl/scraper.ts` с функциями парсинга и обработкой ошибок

### Этап 2: URL утилиты ✅
- Создан `lib/utils/url.ts` с полным набором функций:
  - `extractUrls()` - извлечение URL из текста
  - `isValidUrl()` - валидация URL
  - `normalizeUrl()` - нормализация URL
  - `isPrimaryUrl()` - определение заметок-ссылок
  - Дополнительные утилиты для работы с URL

### Этап 3: Модификация API ✅
- Обновлен `/api/notes/route.ts`:
  - Автоматическое определение типа `'link'` для заметок с URL
  - Поддержка анализа ссылок в фоновом режиме

### Этап 4: Интеграция с AI ✅
- Обновлена `analyzeNoteInBackground()` для поддержки разных типов контента
- Создана `prepareContentForAnalysis()` для парсинга ссылок перед AI анализом
- Реализован fallback на оригинальный URL при сбоях парсинга

### Этап 5: Рефакторинг и оптимизация ✅
- **Модульность**: Создан `lib/firecrawl/config.ts` для констант
- **Логирование**: Создан `lib/firecrawl/logger.ts` для централизованного логирования
- **Типизация**: Улучшена типизация в `scraper.ts` с интерфейсом `FirecrawlResponse`
- **Разделение ответственности**:
  - `lib/utils/content-type.ts` - определение типа контента
  - `lib/content/preparation.ts` - подготовка контента для анализа
- **Упрощение**: Убраны отладочные логи из API routes
- **DRY**: Устранено дублирование логики подготовки контента
- **Читаемость**: Добавлены комментарии к регулярным выражениям URL
- **Константы**: Вынесены магические числа в конфигурацию

## Результаты тестирования
✅ **Полная интеграция работает:**
- Автоматическое определение ссылок как `content_type: 'link'`
- Успешный парсинг содержимого (42,574 символа для тестовой статьи)
- Качественный AI анализ парсенного контента
- Генерация релевантных тегов: `['AI', 'LLM', 'Software 3.0', 'Andrej Karpathy', 'Programming']`
- Детальное саммари с ключевыми концепциями из статьи
- Отказоустойчивость при сбоях Firecrawl
