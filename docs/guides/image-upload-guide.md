# Руководство по загрузке изображений

## Обзор функциональности

Приложение теперь поддерживает загрузку изображений к заметкам с последующим мультимодальным AI анализом. Пользователи могут:

- Прикреплять изображения к заметкам
- Создавать заметки только с изображениями (без текста)
- Получать AI анализ как текста, так и визуального контента
- Просматривать изображения в ленте заметок

## Как использовать

### Создание заметки с изображением

1. **Выбор изображения:**
   - Нажмите кнопку "Изображение" в форме создания заметки
   - Выберите файл изображения (JPEG, PNG, WebP, GIF)
   - Максимальный размер: 10MB

2. **Предпросмотр:**
   - После выбора появится предпросмотр изображения
   - Можно удалить изображение, нажав кнопку "X"

3. **Добавление текста (опционально):**
   - Можно добавить текстовое описание
   - Или создать заметку только с изображением

4. **Отправка:**
   - Нажмите "Отправить"
   - Дождитесь загрузки файла и создания заметки

### AI анализ изображений

После создания заметки с изображением:

1. **Автоматический анализ:**
   - AI анализирует как текст, так и изображение
   - Процесс происходит в фоновом режиме

2. **Генерация тегов:**
   - Создаются теги на основе объектов, людей, мест на изображении
   - Учитывается связь между текстом и визуальным контентом

3. **Создание описания:**
   - Генерируется краткое описание того, что изображено
   - Описание помогает в поиске заметок

## Технические детали

### Хранение файлов
- Изображения хранятся в Supabase Storage
- Используется bucket `note_attachments`
- Каждый пользователь видит только свои файлы

### Безопасность
- Загрузка через signed URLs
- Row Level Security (RLS) политики
- Валидация типов и размеров файлов

### Производительность
- Lazy loading изображений в ленте
- Сжатие изображений на клиенте (рекомендуется)
- Асинхронный AI анализ

## Ограничения

- **Размер файла:** максимум 10MB
- **Форматы:** только JPEG, PNG, WebP, GIF
- **Квоты:** ограничены лимитами Supabase Storage
- **AI анализ:** требует настроенный OpenRouter API ключ

## Устранение неполадок

### Ошибка "Файл слишком большой"
- Уменьшите размер изображения
- Используйте сжатие изображений

### Ошибка "Неподдерживаемый формат"
- Конвертируйте в JPEG, PNG, WebP или GIF
- Проверьте расширение файла

### Изображение не загружается
- Проверьте интернет-соединение
- Убедитесь, что Supabase Storage настроен
- Проверьте квоты хранилища

### AI анализ не работает
- Проверьте наличие OPENROUTER_API_KEY
- Убедитесь в доступности API
- Проверьте логи сервера для деталей
