import { firecrawl, isFirecrawlEnabled } from './client'
import { FIRECRAWL_CONFIG } from './config'
import { firecrawlLogger } from './logger'

export interface ScrapeResult {
  success: boolean
  content?: string
  title?: string
  url?: string
  error?: string
}

interface FirecrawlResponse {
  success: boolean
  markdown?: string
  content?: string
  metadata?: {
    title?: string
  }
  error?: string
}

function extractContentFromResult(result: FirecrawlResponse): { content: string; title: string } {
  const content = result.markdown || result.content || ''
  const title = result.metadata?.title || ''
  return { content, title }
}

function truncateContent(content: string): string {
  if (content.length <= FIRECRAWL_CONFIG.MAX_CONTENT_LENGTH) {
    return content
  }

  return content.substring(0, FIRECRAWL_CONFIG.MAX_CONTENT_LENGTH) + '\n\n[Content truncated...]'
}

export async function scrapeUrl(url: string): Promise<ScrapeResult> {
  if (!isFirecrawlEnabled || !firecrawl) {
    return {
      success: false,
      error: 'Firecrawl is not configured'
    }
  }

  try {
    firecrawlLogger.startScrape(url)
    const startTime = Date.now()

    // Create a timeout promise
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('Scrape timeout')), FIRECRAWL_CONFIG.SCRAPE_TIMEOUT)
    })

    // Race between scrape and timeout
    const scrapePromise = firecrawl.scrapeUrl(url, {
      formats: ['markdown'],
      onlyMainContent: true,
      timeout: FIRECRAWL_CONFIG.SCRAPE_TIMEOUT,
      excludeTags: ['nav', 'footer', 'header', 'aside', 'script', 'style', 'noscript', 'iframe', 'svg', 'form', 'button', 'input']
    })

    const result = await Promise.race([scrapePromise, timeoutPromise]) as FirecrawlResponse
    const duration = Date.now() - startTime

    if (!result.success) {
      firecrawlLogger.scrapeError(result.error)
      return {
        success: false,
        error: result.error || 'Unknown scrape error'
      }
    }

    const { content, title } = extractContentFromResult(result)
    const truncatedContent = truncateContent(content)

    firecrawlLogger.scrapeCompleted(duration, truncatedContent.length)

    return {
      success: true,
      content: truncatedContent,
      title,
      url
    }

  } catch (error) {
    firecrawlLogger.scrapeError(error)

    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error during scraping'
    }
  }
}

export async function scrapeMultipleUrls(urls: string[]): Promise<ScrapeResult[]> {
  if (!isFirecrawlEnabled) {
    return urls.map(url => ({
      success: false,
      url,
      error: 'Firecrawl is not configured'
    }))
  }

  firecrawlLogger.batchStart(urls.length)
  const results: ScrapeResult[] = []

  // Process URLs in parallel but with a reasonable limit
  for (let i = 0; i < urls.length; i += FIRECRAWL_CONFIG.MAX_CONCURRENT_SCRAPES) {
    const batch = urls.slice(i, i + FIRECRAWL_CONFIG.MAX_CONCURRENT_SCRAPES)
    const batchPromises = batch.map(url => scrapeUrl(url))
    const batchResults = await Promise.allSettled(batchPromises)

    batchResults.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        results.push(result.value)
      } else {
        results.push({
          success: false,
          url: batch[index],
          error: result.reason?.message || 'Failed to scrape URL'
        })
      }
    })
  }

  const successfulCount = results.filter(r => r.success).length
  firecrawlLogger.batchCompleted(successfulCount, results.length)

  return results
}
