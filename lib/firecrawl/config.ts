// Firecrawl configuration constants
export const FIRECRAWL_CONFIG = {
  // Timeout for scraping operations (30 seconds)
  SCRAPE_TIMEOUT: 30000,

  // Maximum content length to prevent excessive AI processing costs (50KB)
  MAX_CONTENT_LENGTH: 50000,

  // Maximum concurrent scraping operations
  MAX_CONCURRENT_SCRAPES: 3,

  // Default scraping options
  DEFAULT_SCRAPE_OPTIONS: {
    formats: ['markdown'],
    onlyMainContent: true,
  }
} as const

// URL detection configuration
export const URL_CONFIG = {
  // Maximum length of additional content for a URL to be considered "primary"
  PRIMARY_URL_MAX_ADDITIONAL_CONTENT: 50,
} as const
