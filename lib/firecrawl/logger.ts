// Firecrawl logging utilities
const LOG_PREFIX = '[Firecrawl]'

export const firecrawlLogger = {
  startScrape: (url: string) => {
    console.log(`${LOG_PREFIX} Starting scrape for URL: ${url}`)
  },

  scrapeCompleted: (duration: number, contentLength: number) => {
    console.log(`${LOG_PREFIX} Scrape completed in ${duration}ms`)
    console.log(`${LOG_PREFIX} Successfully scraped ${contentLength} characters`)
  },

  scrapeError: (error: unknown) => {
    console.error(`${LOG_PREFIX} Scrape error:`, error)
  },

  batchStart: (urlCount: number) => {
    console.log(`${LOG_PREFIX} Starting batch scrape for ${urlCount} URLs`)
  },

  batchCompleted: (successful: number, total: number) => {
    console.log(`${LOG_PREFIX} Batch scrape completed: ${successful}/${total} successful`)
  },

  configNotFound: () => {
    console.warn(`${LOG_PREFIX} FIRECRAWL_API_KEY not set, scraping disabled`)
  }
}
