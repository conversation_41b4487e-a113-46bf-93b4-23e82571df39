export const AI_CONSTANTS = {
  CONTENT_PREVIEW_LENGTH: 500,
  SUMMARY_FALLBACK_LENGTH: 100,
  MAX_TAGS_COUNT: 5,
  ANALYZE_TEMPERATURE: 0.3,
  SEARCH_TEMPERATURE: 0.1,
  MAX_TOKENS: 1000
} as const

export function replacePlaceholders(template: string, replacements: Record<string, string>): string {
  let result = template
  for (const [key, value] of Object.entries(replacements)) {
    const placeholder = `{${key}}`
    result = result.replaceAll(placeholder, value)
  }
  return result
}

export function truncateContent(content: string, maxLength: number): string {
  return content.length > maxLength
    ? content.substring(0, maxLength) + '...'
    : content
}

export function prepareNotesForSearch(notes: Array<{
  id: string
  content: string
  tags: string[]
  summary_ai?: string | null
}>) {
  return notes.map(note => ({
    id: note.id,
    content: note.content.substring(0, AI_CONSTANTS.CONTENT_PREVIEW_LENGTH),
    tags: note.tags,
    summary: note.summary_ai || note.content.substring(0, AI_CONSTANTS.SUMMARY_FALLBACK_LENGTH)
  }))
}
