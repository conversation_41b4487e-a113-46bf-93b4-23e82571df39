import { generateObject } from 'ai'
import { getModel, DEFAULT_MODEL, MULTIMODAL_MODEL } from './openrouter'
import { ANALYZE_NOTE_PROMPT } from './prompts'
import { replacePlaceholders, AI_CONSTANTS } from './utils'
import { aiLogger } from './logger'
import { analyzeResultSchema, type AnalyzeResult } from './schemas'

// Constants
const EMPTY_CONTENT_PLACEHOLDER = '[Изображение без текста]'

/**
 * Validate input parameters for analysis
 */
function validateAnalysisInput(content: string, hasImages: boolean): void {
  if ((!content || content.trim().length === 0) && !hasImages) {
    throw new Error('No content to analyze')
  }
}

/**
 * Select appropriate model based on content type
 */
function selectAnalysisModel(hasImages: boolean): string {
  return hasImages ? MULTIMODAL_MODEL : DEFAULT_MODEL
}

/**
 * Prepare messages for AI analysis (text-only or multimodal)
 */
function prepareAnalysisMessages(prompt: string, imageUrls?: string[]) {
  if (imageUrls && imageUrls.length > 0) {
    // Multimodal analysis with one or more images
    const content = [
      { type: 'text' as const, text: prompt },
      ...imageUrls.map(url => ({ type: 'image' as const, image: url }))
    ]

    return [{
      role: 'user' as const,
      content
    }]
  } else {
    // Text-only analysis
    return [{
      role: 'user' as const,
      content: prompt
    }]
  }
}

export async function analyzeNote(content: string, imageUrls?: string[]): Promise<AnalyzeResult> {
  // Select appropriate model and prepare context
  const hasImages = Boolean(imageUrls?.length)
  const modelToUse = selectAnalysisModel(hasImages)

  const logContext = {
    operation: 'analyze' as const,
    model: modelToUse,
    temperature: AI_CONSTANTS.ANALYZE_TEMPERATURE
  }

  try {
    // Validate input - either content or images must be provided
    validateAnalysisInput(content, hasImages)

    // For empty content with images, use special prompt
    const noteContent = content || (hasImages ? EMPTY_CONTENT_PLACEHOLDER : '')

    const prompt = replacePlaceholders(ANALYZE_NOTE_PROMPT, {
      noteContent
    })

    aiLogger.logRequest(logContext, prompt)

    // Prepare messages based on content type
    const messages = prepareAnalysisMessages(prompt, imageUrls)

    const { object } = await generateObject({
      model: getModel(modelToUse),
      messages,
      temperature: AI_CONSTANTS.ANALYZE_TEMPERATURE,
      schema: analyzeResultSchema,
      maxTokens: AI_CONSTANTS.MAX_TOKENS
    })

    aiLogger.logResponse(logContext, object)

    const finalResult = {
      tags: object.tags.slice(0, AI_CONSTANTS.MAX_TAGS_COUNT),
      summary: object.summary
    }

    aiLogger.logResult(logContext, finalResult)

    return finalResult
  } catch (error) {
    aiLogger.logError(logContext, error)

    // Don't provide fallback - let the caller handle the error
    throw error
  }
}
