import { generateObject } from 'ai'
import { getModel, DEFAULT_MODEL, MULTIMODAL_MODEL } from './openrouter'
import { ANALYZE_NOTE_PROMPT } from './prompts'
import { replacePlaceholders, AI_CONSTANTS } from './utils'
import { aiLogger } from './logger'
import { analyzeResultSchema, type AnalyzeResult } from './schemas'

/**
 * Select appropriate model based on content type
 */
function selectAnalysisModel(hasImage: boolean): string {
  return hasImage ? MULTIMODAL_MODEL : DEFAULT_MODEL
}

/**
 * Prepare messages for AI analysis (text-only or multimodal)
 */
function prepareAnalysisMessages(prompt: string, imageUrl?: string) {
  if (imageUrl) {
    // Multimodal analysis with image
    return [{
      role: 'user' as const,
      content: [
        { type: 'text' as const, text: prompt },
        { type: 'image' as const, image: imageUrl }
      ]
    }]
  } else {
    // Text-only analysis
    return [{
      role: 'user' as const,
      content: prompt
    }]
  }
}

export async function analyzeNote(content: string, imageUrl?: string): Promise<AnalyzeResult> {
  // Select appropriate model and prepare context
  const modelToUse = selectAnalysisModel(!!imageUrl)

  const logContext = {
    operation: 'analyze' as const,
    model: modelToUse,
    temperature: AI_CONSTANTS.ANALYZE_TEMPERATURE
  }

  try {
    // Validate input - either content or image must be provided
    if ((!content || content.trim().length === 0) && !imageUrl) {
      throw new Error('No content to analyze')
    }

    // For empty content with image, use special prompt
    const noteContent = content || (imageUrl ? '[Изображение без текста]' : '')

    const prompt = replacePlaceholders(ANALYZE_NOTE_PROMPT, {
      noteContent
    })

    aiLogger.logRequest(logContext, prompt)

    // Prepare messages based on content type
    const messages = prepareAnalysisMessages(prompt, imageUrl)

    const { object } = await generateObject({
      model: getModel(modelToUse),
      messages,
      temperature: AI_CONSTANTS.ANALYZE_TEMPERATURE,
      schema: analyzeResultSchema,
      maxTokens: AI_CONSTANTS.MAX_TOKENS
    })

    aiLogger.logResponse(logContext, object)

    const finalResult = {
      tags: object.tags.slice(0, AI_CONSTANTS.MAX_TAGS_COUNT),
      summary: object.summary
    }

    aiLogger.logResult(logContext, finalResult)

    return finalResult
  } catch (error) {
    aiLogger.logError(logContext, error)

    // Don't provide fallback - let the caller handle the error
    throw error
  }
}
