import { scrapeUrl } from '@/lib/firecrawl/scraper'
import { getFirstUrl } from '@/lib/utils/url'
import type { ContentType } from '@/lib/utils/content-type'

const LOG_PREFIX = '[Content Preparation]'

/**
 * Combines original content with scraped content for better AI analysis context
 */
function combineContentWithScrapedData(
  originalContent: string,
  url: string,
  scrapedContent: string,
  title?: string
): string {
  return [
    `Original note: ${originalContent}`,
    '',
    `Scraped content from ${url}:`,
    `Title: ${title || 'No title'}`,
    '',
    scrapedContent
  ].join('\n')
}

/**
 * Attempts to scrape URL content and falls back to original content on failure
 */
async function scrapeUrlContent(url: string, originalContent: string): Promise<string> {
  console.log(`${LOG_PREFIX} Attempting to scrape URL: ${url}`)
  
  const scrapeResult = await scrapeUrl(url)

  if (scrapeResult.success && scrapeResult.content) {
    console.log(`${LOG_PREFIX} Successfully scraped ${scrapeResult.content.length} characters`)
    return combineContentWithScrapedData(
      originalContent,
      url,
      scrapeResult.content,
      scrapeResult.title
    )
  } else {
    console.log(`${LOG_PREFIX} Scraping failed: ${scrapeResult.error}, using original content`)
    return originalContent
  }
}

/**
 * Prepares content for AI analysis based on content type
 * For link content, attempts to scrape the URL and combine with original content
 * For other types, returns content as-is
 */
export async function prepareContentForAnalysis(
  content: string,
  contentType: ContentType
): Promise<string> {
  if (contentType !== 'link') {
    console.log(`${LOG_PREFIX} Content type is '${contentType}', using original content`)
    return content
  }

  console.log(`${LOG_PREFIX} Processing link content for AI analysis`)

  try {
    const url = getFirstUrl(content)
    if (!url) {
      console.log(`${LOG_PREFIX} No valid URL found in link content, using original content`)
      return content
    }

    console.log(`${LOG_PREFIX} Found URL: ${url}`)
    console.log(`${LOG_PREFIX} Firecrawl enabled: ${process.env.FIRECRAWL_API_KEY ? 'Yes' : 'No'}`)
    
    if (!process.env.FIRECRAWL_API_KEY) {
      console.log(`${LOG_PREFIX} FIRECRAWL_API_KEY not set, using original URL for analysis`)
      return content
    }

    return await scrapeUrlContent(url, content)
  } catch (error) {
    console.error(`${LOG_PREFIX} Error during content preparation:`, error)
    return content
  }
}
