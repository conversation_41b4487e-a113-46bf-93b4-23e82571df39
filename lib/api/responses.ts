import { NextResponse } from 'next/server'
import { DatabaseError } from '@/lib/data/types'

/**
 * Standard API error responses
 */
export const ApiResponses = {
  badRequest: (message: string) => 
    NextResponse.json({ error: message }, { status: 400 }),
    
  unauthorized: (message: string = 'Необходима авторизация') => 
    NextResponse.json({ error: message }, { status: 401 }),
    
  forbidden: (message: string = 'Доступ запрещен') => 
    NextResponse.json({ error: message }, { status: 403 }),
    
  notFound: (message: string = 'Ресурс не найден') => 
    NextResponse.json({ error: message }, { status: 404 }),
    
  serviceUnavailable: (message: string) => 
    NextResponse.json({ error: message }, { status: 503 }),
    
  internalError: (message: string = 'Внутренняя ошибка сервера') => 
    NextResponse.json({ error: message }, { status: 500 }),
    
  success: <T>(data: T, status: number = 200) => 
    NextResponse.json(data, { status }),
    
  created: <T>(data: T) => 
    NextResponse.json(data, { status: 201 })
}

/**
 * Handle common API errors with standardized responses
 */
export function handleApiError(error: unknown): NextResponse {
  console.error('API Error:', error)
  
  if (error instanceof DatabaseError) {
    return ApiResponses.internalError('Ошибка базы данных')
  }
  
  if (error instanceof Error) {
    return ApiResponses.internalError(error.message)
  }
  
  return ApiResponses.internalError()
}
