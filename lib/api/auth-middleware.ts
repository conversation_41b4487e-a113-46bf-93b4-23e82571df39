import { NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export interface AuthenticatedUser {
  id: string
  email?: string
}

export type AuthResult = {
  success: true
  user: AuthenticatedUser
} | {
  success: false
  response: NextResponse
}

/**
 * Middleware to check user authentication
 * Returns either authenticated user or error response
 */
export async function requireAuth(): Promise<AuthResult> {
  try {
    const supabase = await createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return {
        success: false,
        response: NextResponse.json(
          { error: 'Необходима авторизация' },
          { status: 401 }
        )
      }
    }

    return {
      success: true,
      user: {
        id: user.id,
        email: user.email
      }
    }
  } catch (error) {
    console.error('Authentication error:', error)
    return {
      success: false,
      response: NextResponse.json(
        { error: 'Ошибка аутентификации' },
        { status: 500 }
      )
    }
  }
}
