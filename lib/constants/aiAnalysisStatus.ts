/**
 * Centralized constants for AI Analysis Status
 * This ensures synchronization between database schema and TypeScript types
 */

// AI Analysis Status values - must match database enum
export const AI_ANALYSIS_STATUS_VALUES = [
  'pending',
  'processing', 
  'completed',
  'failed',
  'partially_completed'
] as const

// Type derived from the constants array
export type AiAnalysisStatus = typeof AI_ANALYSIS_STATUS_VALUES[number]

// Status check helpers
export const AI_ANALYSIS_STATUS_CHECKS = {
  isPending: (status: AiAnalysisStatus): boolean => status === 'pending',
  isProcessing: (status: AiAnalysisStatus): boolean => status === 'processing',
  isCompleted: (status: AiAnalysisStatus): boolean => status === 'completed',
  isFailed: (status: AiAnalysisStatus): boolean => status === 'failed',
  isPartiallyCompleted: (status: AiAnalysisStatus): boolean => status === 'partially_completed',
  
  // Combined checks
  hasErrors: (status: AiAnalysisStatus): boolean => 
    status === 'failed' || status === 'partially_completed',
  isFinished: (status: AiAnalysisStatus): boolean => 
    status === 'completed' || status === 'failed' || status === 'partially_completed',
  isInProgress: (status: AiAnalysisStatus): boolean => 
    status === 'pending' || status === 'processing'
} as const

// Status display messages
export const AI_ANALYSIS_STATUS_MESSAGES = {
  pending: 'Ожидает анализа',
  processing: 'Анализируется...',
  completed: 'Анализ завершен',
  failed: 'AI анализ не удался',
  partially_completed: 'AI анализ завершен частично'
} as const

// Database constraint string for validation
export const AI_ANALYSIS_STATUS_CONSTRAINT = AI_ANALYSIS_STATUS_VALUES
  .map(status => `'${status}'`)
  .join(', ')
