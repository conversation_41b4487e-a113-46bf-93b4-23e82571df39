import { createClient } from '@/lib/supabase/server'
import { NoteAttachment } from '@/types/notes'
import { DatabaseNoteAttachment, DatabaseError } from './types'
import { ATTACHMENT_LIMITS, ATTACHMENT_ERROR_MESSAGES } from '@/lib/config/attachments'

/**
 * Common error handling for database operations
 */
function handleDatabaseError(operation: string, error: any, customMessage?: string): never {
  console.error(`Database error ${operation}:`, error)
  throw new DatabaseError(customMessage || `Database operation failed: ${operation}`, error)
}

/**
 * Common error handling for unexpected errors
 */
function handleUnexpectedError(operation: string, error: any): never {
  if (error instanceof DatabaseError) {
    throw error
  }
  console.error(`Unexpected error ${operation}:`, error)
  throw new DatabaseError(`Unexpected error occurred while ${operation}`)
}

/**
 * Get all attachments for a specific note
 */
export async function getAttachmentsByNoteId(noteId: string): Promise<NoteAttachment[]> {
  try {
    const supabase = await createClient()

    const { data: attachments, error } = await supabase
      .from('note_attachments')
      .select('*')
      .eq('note_id', noteId)
      .order('created_at', { ascending: true })

    if (error) {
      handleDatabaseError('fetching attachments', error, ATTACHMENT_ERROR_MESSAGES.FETCH_FAILED)
    }

    return formatAttachmentsFromDatabase(attachments || [])
  } catch (error) {
    handleUnexpectedError('fetching attachments', error)
  }
}

/**
 * Validate attachment limits before creation
 */
async function validateAttachmentLimits(
  supabase: any,
  noteId: string,
  newAttachmentsCount: number
): Promise<void> {
  if (newAttachmentsCount > ATTACHMENT_LIMITS.MAX_PER_NOTE) {
    throw new DatabaseError(ATTACHMENT_ERROR_MESSAGES.MAX_LIMIT_EXCEEDED(newAttachmentsCount))
  }

  const { count: currentCount, error } = await supabase
    .from('note_attachments')
    .select('*', { count: 'exact', head: true })
    .eq('note_id', noteId)

  if (error) {
    handleDatabaseError('checking attachment count', error, ATTACHMENT_ERROR_MESSAGES.COUNT_CHECK_FAILED)
  }

  const totalAfterInsert = (currentCount || 0) + newAttachmentsCount
  if (totalAfterInsert > ATTACHMENT_LIMITS.MAX_PER_NOTE) {
    throw new DatabaseError(ATTACHMENT_ERROR_MESSAGES.LIMIT_WOULD_EXCEED(newAttachmentsCount, currentCount || 0))
  }
}

/**
 * Create multiple attachments for a note
 */
export async function createAttachments(
  noteId: string,
  attachmentUrls: string[]
): Promise<NoteAttachment[]> {
  try {
    if (!attachmentUrls.length) {
      return []
    }

    const supabase = await createClient()

    // Validate limits
    await validateAttachmentLimits(supabase, noteId, attachmentUrls.length)

    // Prepare attachment records
    const attachmentRecords = attachmentUrls.map(url => ({
      note_id: noteId,
      url
    }))

    const { data: attachments, error } = await supabase
      .from('note_attachments')
      .insert(attachmentRecords)
      .select()

    if (error) {
      handleDatabaseError('creating attachments', error, ATTACHMENT_ERROR_MESSAGES.CREATE_FAILED)
    }

    return formatAttachmentsFromDatabase(attachments || [])
  } catch (error) {
    handleUnexpectedError('creating attachments', error)
  }
}

/**
 * Verify attachment ownership
 */
async function verifyAttachmentOwnership(
  supabase: any,
  attachmentId: string,
  userId: string
): Promise<void> {
  const { data: attachment, error } = await supabase
    .from('note_attachments')
    .select(`
      id,
      notes!inner (
        user_id
      )
    `)
    .eq('id', attachmentId)
    .single()

  if (error || !attachment) {
    throw new DatabaseError(ATTACHMENT_ERROR_MESSAGES.ACCESS_DENIED)
  }

  // Type assertion for the nested structure
  const attachmentWithNote = attachment as any
  if (attachmentWithNote.notes.user_id !== userId) {
    throw new DatabaseError(ATTACHMENT_ERROR_MESSAGES.USER_ACCESS_DENIED)
  }
}

/**
 * Delete a specific attachment
 */
export async function deleteAttachment(attachmentId: string, userId: string): Promise<void> {
  try {
    const supabase = await createClient()

    // Verify ownership
    await verifyAttachmentOwnership(supabase, attachmentId, userId)

    const { error } = await supabase
      .from('note_attachments')
      .delete()
      .eq('id', attachmentId)

    if (error) {
      handleDatabaseError('deleting attachment', error, ATTACHMENT_ERROR_MESSAGES.DELETE_FAILED)
    }
  } catch (error) {
    handleUnexpectedError('deleting attachment', error)
  }
}

/**
 * Delete all attachments for a note (used when deleting a note)
 */
export async function deleteAttachmentsByNoteId(noteId: string): Promise<void> {
  try {
    const supabase = await createClient()

    const { error } = await supabase
      .from('note_attachments')
      .delete()
      .eq('note_id', noteId)

    if (error) {
      handleDatabaseError('deleting attachments by note ID', error, ATTACHMENT_ERROR_MESSAGES.DELETE_FAILED)
    }
  } catch (error) {
    handleUnexpectedError('deleting attachments by note ID', error)
  }
}

/**
 * Get attachment count for a note
 */
export async function getAttachmentCount(noteId: string): Promise<number> {
  try {
    const supabase = await createClient()

    const { count, error } = await supabase
      .from('note_attachments')
      .select('*', { count: 'exact', head: true })
      .eq('note_id', noteId)

    if (error) {
      handleDatabaseError('counting attachments', error, ATTACHMENT_ERROR_MESSAGES.COUNT_CHECK_FAILED)
    }

    return count || 0
  } catch (error) {
    handleUnexpectedError('counting attachments', error)
  }
}

/**
 * Save analysis error for a specific attachment
 */
export async function saveAttachmentAnalysisError(
  noteId: string,
  attachmentUrl: string,
  error: any
): Promise<void> {
  try {
    const supabase = await createClient()

    // Convert error to string for storage
    const errorMessage = error instanceof Error ? error.message : String(error)

    const { error: updateError } = await supabase
      .from('note_attachments')
      .update({ analysis_error: errorMessage })
      .eq('note_id', noteId)
      .eq('url', attachmentUrl)

    if (updateError) {
      handleDatabaseError('saving attachment analysis error', updateError, 'Failed to save attachment analysis error')
    }
  } catch (error) {
    handleUnexpectedError('saving attachment analysis error', error)
  }
}

/**
 * Clear analysis errors for all attachments of a note
 */
export async function clearAttachmentAnalysisErrors(noteId: string): Promise<void> {
  try {
    const supabase = await createClient()

    const { error } = await supabase
      .from('note_attachments')
      .update({ analysis_error: null })
      .eq('note_id', noteId)

    if (error) {
      handleDatabaseError('clearing attachment analysis errors', error, 'Failed to clear attachment analysis errors')
    }
  } catch (error) {
    handleUnexpectedError('clearing attachment analysis errors', error)
  }
}

/**
 * Format database attachments to client format
 */
function formatAttachmentsFromDatabase(attachments: DatabaseNoteAttachment[]): NoteAttachment[] {
  return attachments.map(attachment => ({
    id: attachment.id,
    note_id: attachment.note_id,
    url: attachment.url,
    created_at: attachment.created_at,
    analysis_error: attachment.analysis_error
  }))
}
