import { AiAnalysisStatus } from '@/lib/constants/aiAnalysisStatus'

export type { AiAnalysisStatus }

export interface DatabaseNoteAttachment {
  id: string
  note_id: string
  url: string
  created_at: string
  analysis_error?: string | null
}

export interface DatabaseNote {
  id: string
  user_id?: string
  content: string
  content_type: 'text' | 'file' | 'link'
  summary_ai: string | null
  ai_analysis_status: AiAnalysisStatus
  created_at: string
  updated_at: string
  note_tags?: {
    tags: {
      id: string
      name: string
    }[]
  }[]
  note_attachments?: DatabaseNoteAttachment[]
}

export interface DatabaseTag {
  id: string
  name: string
}

export interface DatabaseNoteTag {
  note_id: string
  tag_id: string
}

export class DatabaseError extends Error {
  constructor(message: string, public originalError?: any) {
    super(message)
    this.name = 'DatabaseError'
  }
}
