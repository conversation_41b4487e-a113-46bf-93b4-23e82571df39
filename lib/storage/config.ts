// Supabase Storage configuration for note attachments

export const STORAGE_CONFIG = {
  // Bucket name for note attachments
  BUCKET_NAME: 'note_attachments',

  // Maximum file size (10MB)
  MAX_FILE_SIZE: 10 * 1024 * 1024,

  // Allowed MIME types for images
  ALLOWED_MIME_TYPES: [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/webp',
    'image/gif'
  ] as const,

  // File extensions mapping
  ALLOWED_EXTENSIONS: [
    '.jpg',
    '.jpeg',
    '.png',
    '.webp',
    '.gif'
  ] as const,

  // Signed URL expiration time (1 hour)
  SIGNED_URL_EXPIRES_IN: 3600,

  // Upload URL expiration time (10 minutes)
  UPLOAD_URL_EXPIRES_IN: 600
} as const

export type AllowedMimeType = typeof STORAGE_CONFIG.ALLOWED_MIME_TYPES[number]
export type AllowedExtension = typeof STORAGE_CONFIG.ALLOWED_EXTENSIONS[number]

// Error messages for file validation
export const STORAGE_ERRORS = {
  FILE_TOO_LARGE: (maxSize: string) => `Файл слишком большой. Максимальный размер: ${maxSize}`,
  INVALID_FILE_TYPE: 'Неподдерживаемый тип файла. Разрешены только изображения (JPEG, PNG, WebP, GIF)',
  INVALID_FILE_EXTENSION: 'Неподдерживаемое расширение файла',
  UPLOAD_FAILED: 'Не удалось загрузить файл',
  UPLOAD_URL_FAILED: 'Не удалось создать URL для загрузки файла'
} as const
