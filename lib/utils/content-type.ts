import { isPrimaryUrl } from './url'

export type ContentType = 'text' | 'file' | 'link'

/**
 * Automatically detects the content type based on the content string
 * @param content - The content to analyze
 * @returns The detected content type
 */
export function detectContentType(content: string): ContentType {
  if (isPrimaryUrl(content)) {
    return 'link'
  }
  
  return 'text'
}

/**
 * Determines the final content type, preferring explicit type over auto-detection
 * @param content - The content to analyze
 * @param explicitType - Explicitly provided content type (optional)
 * @returns The final content type to use
 */
export function determineContentType(content: string, explicitType?: ContentType): ContentType {
  if (explicitType) {
    return explicitType
  }
  
  return detectContentType(content)
}
