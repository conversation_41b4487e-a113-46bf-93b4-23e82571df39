import { NextRequest } from 'next/server'
import { generateUploadUrl } from '@/lib/storage/upload'
import { requireAuth } from '@/lib/api/auth-middleware'
import { ApiResponses, handleApiError } from '@/lib/api/responses'
import { ATTACHMENT_LIMITS, ATTACHMENT_ERROR_MESSAGES } from '@/lib/config/attachments'
import { z } from 'zod'

// Validation schemas
const fileInfoSchema = z.object({
  fileName: z.string().min(1, ATTACHMENT_ERROR_MESSAGES.FILE_NAME_REQUIRED),
  fileSize: z.number().positive(ATTACHMENT_ERROR_MESSAGES.FILE_SIZE_POSITIVE),
  fileType: z.string().min(1, ATTACHMENT_ERROR_MESSAGES.FILE_TYPE_REQUIRED)
})

const uploadUrlRequestSchema = z.object({
  files: z.array(fileInfoSchema)
    .min(ATTACHMENT_LIMITS.MIN_PER_REQUEST, ATTACHMENT_ERROR_MESSAGES.MIN_FILES_REQUIRED)
    .max(ATTACHMENT_LIMITS.MAX_PER_NOTE, ATTACHMENT_ERROR_MESSAGES.MAX_FILES_EXCEEDED)
})

// Types
export type UploadUrlRequest = z.infer<typeof uploadUrlRequestSchema>

export interface UploadUrlResponse {
  uploadUrl: string
  filePath: string
  publicUrl: string
}

export interface MultipleUploadUrlResponse {
  uploads: UploadUrlResponse[]
}

/**
 * Validate request body and extract files data
 */
function validateRequestBody(body: any): { success: true; files: any[] } | { success: false; error: string } {
  const validation = uploadUrlRequestSchema.safeParse(body)

  if (!validation.success) {
    console.error('Upload URL validation error:', validation.error)
    const errorMessage = validation.error.errors.map(e => e.message).join(', ')
    return { success: false, error: `Ошибка валидации: ${errorMessage}` }
  }

  return { success: true, files: validation.data.files }
}

/**
 * Generate upload URLs for multiple files
 */
async function generateMultipleUploadUrls(userId: string, files: any[]): Promise<UploadUrlResponse[]> {
  const uploads: UploadUrlResponse[] = []

  for (const file of files) {
    const result = await generateUploadUrl(userId, {
      name: file.fileName,
      size: file.fileSize,
      type: file.fileType
    })

    if ('error' in result) {
      throw new Error(result.error)
    }

    uploads.push(result as UploadUrlResponse)
  }

  return uploads
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const authResult = await requireAuth()
    if (!authResult.success) {
      return authResult.response
    }

    // Parse and validate request body
    const body = await request.json()
    const validation = validateRequestBody(body)

    if (!validation.success) {
      return ApiResponses.badRequest(validation.error)
    }

    // Generate upload URLs for all files
    const uploads = await generateMultipleUploadUrls(authResult.user.id, validation.files)

    return ApiResponses.success({ uploads } as MultipleUploadUrlResponse)
  } catch (error) {
    return handleApiError(error)
  }
}
